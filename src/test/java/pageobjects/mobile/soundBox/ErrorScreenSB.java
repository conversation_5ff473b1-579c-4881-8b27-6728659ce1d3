package pageobjects.mobile.soundBox;

import org.openqa.selenium.By;
import com.one97.paytm.appautomation.utils.ActionHelper;

public class ErrorScreenSB {
    private static ErrorScreenSB instance;
    private static final String ERROR_OK_BUTTON = "android.widget.Button";
                                                   
    public static ErrorScreenSB getInstance() {
        if (instance == null) {
            instance = new ErrorScreenSB();
        }
        return instance;
    }

    public boolean isErrorScreenDisplayed() {
        return ActionHelper.isPresentWithWait(By.className(ERROR_OK_BUTTON), 5000);
    }

    public void clickOkButton() {
        ActionHelper.click(By.className(ERROR_OK_BUTTON));
    }
} 