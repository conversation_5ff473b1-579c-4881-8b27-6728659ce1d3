package testsuite.functionalmobile.mapSoundboxFlow;

import base.BaseTestClass;
import com.one97.paytm.appautomation.annotations.TestConfig;
import com.one97.paytm.appautomation.enums.Platform;
import com.one97.paytm.appautomation.utils.ActionHelper;
import org.testng.Assert;
import org.testng.annotations.*;

import pageobjects.mobile.soundBox.*;
import org.testng.annotations.Listeners;
import com.aventstack.chaintest.plugins.ChainTestListener;


@Listeners(ChainTestListener.class)
public class ExecuteSuite_SBPlanUpgrade extends BaseTestClass {

    private final String MOBILE_NUMBER = "7771110999";
    private final String POS_ID = "Pos51";
    private final String OTP = "888888";
    private final String PAYMENT_TYPE = "sound_box";
    private final String PAYMENT_ENV = "releaseNovW2";
    private final String PLAN_NAME = "PU Simcharge1 English";
    private HomePage homePage;
    private SoundboxDeviceManagementNew deviceManagement;
    private MidDetailsPage midDetailsPage;
    private DeviceSelectionSB deviceSelection;
    private SBDeviceSnannerInput deviceScanner;
    private PlanSelectionSB planSelection;
    private TNCAcceptance tncAcceptance;
    private BusinessAgreementSB businessAgreement;
    private PaymentScreenSB paymentScreen;
    private ErrorScreenSB errorScreen;
    private RevieOrderSB reviewOrder;
    private String selectedDeviceId;
    private PlanUpgradeSuccessSB successScreen;

    private static final int MAX_RETRIES = 3;

   


    @Test(priority = 1)
    @TestConfig(platform = {Platform.ANDROID}, launchApp = true)
    public void testLaunchAndNavigateToUpgrade() {
        homePage = HomePage.getInstance();
        homePage.GGAPPLaunchSBFlow(MOBILE_NUMBER, OTP);

        deviceManagement = new SoundboxDeviceManagementNew();
        Assert.assertTrue(deviceManagement.isUpgradePlanVisible(), "Upgrade Plan option should be visible");
        deviceManagement.clickUpgradePlan();
    }

    @Test(priority = 2, dependsOnMethods = "testLaunchAndNavigateToUpgrade")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testMidSelection() {
        int retryCount = 0;
        boolean success = false;

        while (!success && retryCount < MAX_RETRIES) {
            try {
                errorScreen = ErrorScreenSB.getInstance();
                if (errorScreen.isErrorScreenDisplayed()) {
                    errorScreen.clickOkButton();
                    testLaunchAndNavigateToUpgrade();
                }
                midDetailsPage = MidDetailsPage.getInstance();
                // Check for loader layout on MID page and handle it if present
                //ActionHelper.scrollPageUp();
                Thread.sleep(3000);
                ActionHelper.navigateBack();
                homePage.GGAPPLaunchSBFlow(MOBILE_NUMBER, OTP);
                deviceManagement = new SoundboxDeviceManagementNew();
                Assert.assertTrue(deviceManagement.isUpgradePlanVisible(), "Upgrade Plan option should be visible");
                deviceManagement.clickUpgradePlan();
                Assert.assertTrue(midDetailsPage.isTitleVisible(), "MID Details page should be visible");
                midDetailsPage.midSelectionAndProceed();
                success = true;
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= MAX_RETRIES) {
                    throw new RuntimeException("Failed to complete MID selection after " + MAX_RETRIES + " attempts", e);
                }
            }
        }
    }

    @Test(priority = 3, dependsOnMethods = "testMidSelection")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testDeviceSelection() {
        int retryCount = 0;
        boolean success = false;

        while (!success && retryCount < MAX_RETRIES) {
            try {
                errorScreen = ErrorScreenSB.getInstance();
                if (errorScreen.isErrorScreenDisplayed()) {
                    errorScreen.clickOkButton();
                   // testLaunchAndNavigateToUpgrade();
                    testMidSelection();
                }

                deviceSelection = DeviceSelectionSB.getInstance();
                // Check for loader layout on device selection page and handle it if present
               
                Assert.assertTrue(deviceSelection.isSelectionHeaderVisible(), "Device selection page should be visible");
                selectedDeviceId = deviceSelection.selectDeviceByPosIdAndGetId(POS_ID);
                deviceSelection.clickNext();
                success = true;
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= MAX_RETRIES) {
                    throw new RuntimeException("Failed to complete device selection after " + MAX_RETRIES + " attempts", e);
                }
            }
        }
    }

    @Test(priority = 4, dependsOnMethods = "testDeviceSelection")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testQRCodeBypass() {
        deviceScanner = SBDeviceSnannerInput.getInstance();
        deviceScanner.clickByPassQrCta();
        deviceScanner.enterAndSubmitQRString(selectedDeviceId);
    }

    @Test(priority = 5, dependsOnMethods = "testQRCodeBypass")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testPlanSelection() {
        planSelection = PlanSelectionSB.getInstance();
        planSelection.selectPlanByName(PLAN_NAME);
        planSelection.clickProceed();
        Assert.assertTrue(planSelection.isConfirmPlanScreenDisplayed(), "Confirm Plan screen should be visible");
        planSelection.clickProceed();
        reviewOrder = RevieOrderSB.getInstance();
        Assert.assertTrue(reviewOrder.isReviewOrderTitleVisible(), "Review Order screen should be visible");
        reviewOrder.clickProceedButton();
    }



    @Test(priority = 6, dependsOnMethods = "testPlanSelection")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testTNCAcceptance() {
        tncAcceptance = TNCAcceptance.getInstance();
        Assert.assertTrue(tncAcceptance.isDisplayed_OkButton(), "TNC acceptance page should be visible");
        tncAcceptance.click_OkButton();
    }

    @Test(priority = 7, dependsOnMethods = "testTNCAcceptance")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testBusinessAgreement() {
        businessAgreement = BusinessAgreementSB.getInstance();
        Assert.assertTrue(businessAgreement.isOtpVerificationScreenDisplayed(), "OTP verification screen should be visible");
        businessAgreement.enterOtpAndProceed(OTP);
    }

    @Test(priority = 8, dependsOnMethods = "testBusinessAgreement")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testPaymentProcessing() {
        paymentScreen = PaymentScreenSB.getInstance();
        String paymentStatus = paymentScreen.makePayment(MOBILE_NUMBER, PAYMENT_TYPE, PAYMENT_ENV);
        Assert.assertTrue(paymentScreen.handlePaymentSuccessPopup(), "Payment should be successful");
    }

    @Test(priority = 9, dependsOnMethods = "testPaymentProcessing")
    @TestConfig(platform = {Platform.ANDROID}, launchApp = false)
    public void testSuccessScreen() {
        successScreen = PlanUpgradeSuccessSB.getInstance();
        Assert.assertTrue(successScreen.isSuccessScreenDisplayed(), "Plan upgrade success message should be visible");
        Assert.assertTrue(successScreen.isGoToHomeButtonDisplayed(), "Go to Home button should be visible");
        successScreen.clickGoToHome();
    }


}